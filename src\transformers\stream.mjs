/**
 * Stream processing functions that handle server-sent events from Gemini API
 * and transform them to OpenAI-compatible streaming format.
 */
import { transform<PERSON>and<PERSON><PERSON><PERSON><PERSON><PERSON>, checkPromptBlock, transformUsage } from './response.mjs';
import { generateId } from '../utils/helpers.mjs';
import { RESPONSE_LINE_REGEX, STREAM_DELIMITER } from '../constants/index.mjs';

/**
 * Formats an object as a server-sent event data line.
 * Adds timestamp and proper SSE formatting for OpenAI compatibility.
 *
 * @param {Object} obj - Object to format as SSE data
 * @returns {string} Formatted SSE line with data prefix and delimiter
 */
export const sseline = (obj) => {
  obj.created = Math.floor(Date.now()/1000);
  return "data: " + JSON.stringify(obj) + STREAM_DELIMITER;
};

/**
 * Process streaming candidate with thinking content accumulation.
 * Accumulates thinking parts across multiple chunks and sends them as a single block.
 *
 * @param {Object} cand - Gemini candidate object
 * @param {string} thinkingMode - Thinking mode for content processing
 * @returns {Object} OpenAI choice object with delta property
 * @this {Object} Transform stream context with thinking accumulation state
 */
function processStreamingCandidate(cand, thinkingMode) {
  const message = { role: "assistant", content: [] };
  const thinkingParts = [];
  const regularParts = [];

  // Process each content part (text, function calls, or thoughts)
  for (const part of cand.content?.parts ?? []) {
    if (part.functionCall) {
      const fc = part.functionCall;
      message.tool_calls = message.tool_calls ?? [];
      message.tool_calls.push({
        id: fc.id ?? "call_" + generateId(),
        type: "function",
        function: {
          name: fc.name,
          arguments: JSON.stringify(fc.args),
        }
      });
    } else if (part.text) {
      // Separate thinking parts from regular content
      if (part.thought === true) {
        // Remove existing <think> tags if present to avoid nested tags
        const cleanText = part.text.replace(/<\/?think>/g, '');
        thinkingParts.push(cleanText);
      } else {
        regularParts.push(part.text);
      }
    }
  }

  // Handle thinking mode with accumulation
  let content = null;
  if (thinkingMode === "thinking") {
    // Accumulate thinking parts
    if (thinkingParts.length > 0) {
      this.thinkingAccumulator.push(...thinkingParts);
    }

    // Check if we have regular content (indicates thinking is done)
    if (regularParts.length > 0 && !this.hasFinishedThinking) {
      // Send accumulated thinking content as one block
      if (this.thinkingAccumulator.length > 0) {
        this.hasFinishedThinking = true;
        const accumulatedThinking = `<think>${this.thinkingAccumulator.join('\n\n')}</think>`;
        content = accumulatedThinking + '\n\n' + regularParts.join('\n\n');
      } else {
        content = regularParts.join('\n\n');
      }
    } else if (regularParts.length > 0 && this.hasFinishedThinking) {
      // Just regular content after thinking is done
      content = regularParts.join('\n\n');
    } else if (thinkingParts.length > 0 && !this.hasStartedThinking) {
      // Don't send thinking content yet, just accumulate
      this.hasStartedThinking = true;
      content = null;
    } else {
      // Continue accumulating or no content
      content = null;
    }
  } else {
    // Use original logic for non-thinking modes
    if (thinkingMode === "refined") {
      content = regularParts.length > 0 ? regularParts.join('\n\n') : null;
    } else {
      const allParts = [...thinkingParts, ...regularParts];
      content = allParts.length > 0 ? allParts.join('\n\n') : null;
    }
  }

  message.content = content;

  return {
    index: cand.index || 0,
    delta: message,
    logprobs: null,
    finish_reason: message.tool_calls ? "tool_calls" : (cand.finishReason ? (cand.finishReason === "STOP" ? "stop" : cand.finishReason) : null),
  };
}

/**
 * Transform stream function that parses incoming Gemini SSE chunks.
 * Buffers partial data and extracts complete JSON objects for processing.
 *
 * @param {string} chunk - Raw text chunk from Gemini stream
 * @param {TransformStreamDefaultController} controller - Stream controller for output
 * @this {Object} Transform stream context with buffer property
 */
export function parseStream(chunk, controller) {
  this.buffer += chunk;
  do {
    const match = this.buffer.match(RESPONSE_LINE_REGEX);
    if (!match) { break; }
    controller.enqueue(match[1]);
    this.buffer = this.buffer.substring(match[0].length);
  } while (true); // eslint-disable-line no-constant-condition
}

/**
 * Flush function that handles any remaining buffered data when stream ends.
 * Logs errors for incomplete data and marks buffer issues for downstream handling.
 *
 * @param {TransformStreamDefaultController} controller - Stream controller for output
 * @this {Object} Transform stream context with buffer and shared properties
 */
export function parseStreamFlush(controller) {
  if (this.buffer) {
    console.error("Invalid data:", this.buffer);
    controller.enqueue(this.buffer);
    this.shared.is_buffers_rest = true;
  }
}

/**
 * Transform stream function that converts Gemini streaming responses to OpenAI format.
 * Handles candidate processing, content filtering, and streaming protocol compliance.
 *
 * @param {string} line - JSON string from parsed Gemini stream
 * @param {TransformStreamDefaultController} controller - Stream controller for output
 * @this {Object} Transform stream context with id, model, thinkingMode, and other properties
 */
export function toOpenAiStream(line, controller) {
  let data;
  try {
    data = JSON.parse(line);
    if (!data.candidates) {
      throw new Error("Invalid completion chunk object");
    }
  } catch (err) {
    console.error("Error parsing response:", err);
    if (!this.shared.is_buffers_rest) { line =+ STREAM_DELIMITER; }
    controller.enqueue(line);
    return;
  }

  // Initialize thinking accumulator if not exists
  if (!this.thinkingAccumulator) {
    this.thinkingAccumulator = [];
  }
  if (!this.hasStartedThinking) {
    this.hasStartedThinking = false;
  }
  if (!this.hasFinishedThinking) {
    this.hasFinishedThinking = false;
  }

  // Build OpenAI-compatible streaming chunk
  const obj = {
    id: this.id,
    choices: data.candidates.map(cand => processStreamingCandidate.call(this, cand, this.thinkingMode)),
    model: data.modelVersion ?? this.model,
    object: "chat.completion.chunk",
    usage: data.usageMetadata && this.streamIncludeUsage ? null : undefined,
  };

  // Debug logging for streaming thinking content
  if (this.thinkingMode === "thinking" && obj.choices.length > 0) {
    obj.choices.forEach((choice, index) => {
      if (choice.delta && choice.delta.content) {
        console.log("=== STREAMING THINKING DEBUG ===");
        console.log("Stream ID:", this.id);
        console.log("Choice index:", index);
        console.log("Delta content length:", choice.delta.content.length);
        console.log("Delta content:", JSON.stringify(choice.delta.content));
        console.log("=== END STREAMING DEBUG ===");
      }
    });
  }

  // Handle content filtering blocks
  if (checkPromptBlock(obj.choices, data.promptFeedback, "delta")) {
    controller.enqueue(sseline(obj));
    return;
  }

  console.assert(data.candidates.length === 1, "Unexpected candidates count: %d", data.candidates.length);
  const cand = obj.choices[0];
  cand.index = cand.index || 0;
  const finish_reason = cand.finish_reason;
  cand.finish_reason = null;

  // Send initial chunk with role for new candidates
  if (!this.last[cand.index]) {
    controller.enqueue(sseline({
      ...obj,
      choices: [{ ...cand, tool_calls: undefined, delta: { role: "assistant", content: "" } }],
    }));
  }

  // Send content delta if present
  delete cand.delta.role;
  if ("content" in cand.delta) {
    // Log the actual SSE line being sent for thinking mode
    if (this.thinkingMode === "thinking") {
      const sseData = sseline(obj);
      console.log("=== SSE LINE DEBUG ===");
      console.log("SSE data being sent:", sseData);
      console.log("=== END SSE DEBUG ===");
    }
    controller.enqueue(sseline(obj));
  }

  // Prepare final chunk with finish reason and usage
  cand.finish_reason = finish_reason;
  if (data.usageMetadata && this.streamIncludeUsage) {
    obj.usage = transformUsage(data.usageMetadata);
  }
  cand.delta = {};
  this.last[cand.index] = obj;
}

/**
 * Flush function that sends final chunks and stream termination signal.
 * Outputs any pending finish reasons and the required [DONE] marker.
 *
 * @param {TransformStreamDefaultController} controller - Stream controller for output
 * @this {Object} Transform stream context with last array property
 */
export function toOpenAiStreamFlush(controller) {
  if (this.last.length > 0) {
    for (const obj of this.last) {
      controller.enqueue(sseline(obj));
    }
    controller.enqueue("data: [DONE]" + STREAM_DELIMITER);
  }
}
